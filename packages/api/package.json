{"name": "@acme/api", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@acme/auth": "workspace:*", "@acme/db": "workspace:*", "@acme/validators": "workspace:*", "@trpc/server": "catalog:", "superjson": "2.2.2", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}