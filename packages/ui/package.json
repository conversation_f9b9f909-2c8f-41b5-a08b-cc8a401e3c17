{"name": "@acme/ui", "private": true, "type": "module", "exports": {".": "./src/index.ts", "./button": "./src/button.tsx", "./dropdown-menu": "./src/dropdown-menu.tsx", "./form": "./src/form.tsx", "./input": "./src/input.tsx", "./label": "./src/label.tsx", "./theme": "./src/theme.tsx", "./toast": "./src/toast.tsx"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "ui-add": "pnpm dlx shadcn@latest add && prettier src --write --list-different"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-icons": "^1.3.2", "class-variance-authority": "^0.7.1", "next-themes": "^0.4.6", "radix-ui": "^1.4.3", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/react": "catalog:react19", "eslint": "catalog:", "prettier": "catalog:", "react": "catalog:react19", "typescript": "catalog:", "zod": "catalog:"}, "peerDependencies": {"react": "catalog:react19", "zod": "catalog:"}, "prettier": "@acme/prettier-config"}