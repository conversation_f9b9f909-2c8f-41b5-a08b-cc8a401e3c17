{"name": "create-t3-turbo", "private": true, "engines": {"node": ">=22.19.0", "pnpm": ">=10.15.1"}, "packageManager": "pnpm@10.15.1", "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo run clean", "auth:generate": "pnpm -F @acme/auth generate", "db:push": "turbo -F @acme/db push", "db:studio": "turbo -F @acme/db studio", "dev": "turbo watch dev --continue", "dev:next": "turbo watch dev -F @acme/nextjs...", "format": "turbo run format --continue -- --cache --cache-location .cache/.prettiercache", "format:fix": "turbo run format --continue -- --write --cache --cache-location .cache/.prettiercache", "lint": "turbo run lint --continue -- --cache --cache-location .cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location .cache/.eslintcache", "lint:ws": "pnpm dlx sherif@latest", "postinstall": "pnpm lint:ws", "typecheck": "turbo run typecheck", "ui-add": "turbo run ui-add", "android": "expo run:android", "ios": "expo run:ios"}, "devDependencies": {"@acme/prettier-config": "workspace:*", "@turbo/gen": "^2.5.6", "prettier": "catalog:", "turbo": "^2.5.6", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}