{"name": "@acme/eslint-config", "private": true, "type": "module", "exports": {"./base": "./base.js", "./nextjs": "./nextjs.js", "./react": "./react.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@eslint/compat": "^1.3.2", "@next/eslint-plugin-next": "^15.5.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "eslint-plugin-turbo": "^2.5.6", "typescript-eslint": "^8.42.0"}, "devDependencies": {"@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}