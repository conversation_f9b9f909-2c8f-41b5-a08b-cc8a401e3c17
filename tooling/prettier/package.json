{"name": "@acme/prettier-config", "private": true, "type": "module", "exports": {".": "./index.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.7.0", "prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.14"}, "devDependencies": {"@acme/tsconfig": "workspace:*", "@types/node": "^22.18.1", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}