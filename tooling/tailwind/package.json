{"name": "@acme/tailwind-config", "private": true, "type": "module", "exports": {"./native": "./native.ts", "./web": "./web.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "^8.5.6", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "^22.18.1", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}