{"name": "@acme/expo", "private": true, "main": "index.ts", "scripts": {"clean": "git clean -xdf .cache .expo .turbo android ios node_modules", "dev": "expo start", "dev:android": "expo start --android", "dev:ios": "expo start --ios", "android": "expo run:android", "ios": "expo run:ios", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path .prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"@better-auth/expo": "1.3.8", "@expo/metro-config": "^0.20.14", "@legendapp/list": "^2.0.2", "@tanstack/react-query": "catalog:", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@trpc/tanstack-react-query": "catalog:", "better-auth": "1.3.8", "expo": "53.0.9", "expo-constants": "17.1.6", "expo-dev-client": "5.1.8", "expo-linking": "7.1.5", "expo-router": "5.0.7", "expo-secure-store": "14.2.3", "expo-splash-screen": "0.30.8", "expo-status-bar": "2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "14.1.6", "nativewind": "~4.1.23", "react": "catalog:react19", "react-dom": "catalog:react19", "react-native": "0.79.2", "react-native-gesture-handler": "~2.25.0", "react-native-reanimated": "~3.18.0", "react-native-safe-area-context": "~5.4.1", "react-native-screens": "~4.11.1", "superjson": "2.2.2"}, "devDependencies": {"@acme/api": "workspace:*", "@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@babel/core": "^7.28.4", "@babel/preset-env": "^7.28.3", "@babel/runtime": "^7.28.4", "@types/babel__core": "^7.20.5", "@types/react": "catalog:react19", "eslint": "catalog:", "prettier": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}